/**
 * 实时数据更新Hook
 * 用于管理WebSocket连接和实时数据更新
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { RealtimeService, RealtimeMessage, getRealtimeService, initRealtimeService } from '../services/realtimeService'

interface UseRealTimeDataOptions {
  enabled?: boolean
  wsUrl?: string
  apiUrl?: string
  onUserActivity?: (data: any) => void
  onTokensUpdate?: (data: any) => void
  onCommentGenerated?: (data: any) => void
  onSystemStatus?: (data: any) => void
  onConfigUpdate?: (data: any) => void
  syncConfig?: {
    enableUserSync: boolean
    enableTokensSync: boolean
    enableCommentsSync: boolean
    enableActivitiesSync: boolean
    syncInterval: number
  }
}

export const useRealTimeData = (options: UseRealTimeDataOptions = {}) => {
  // 🚫 WebSocket功能已完全禁用 - 直接返回禁用状态
  console.log('🚫 useRealTimeData已禁用，使用HTTP API替代WebSocket')
  
  return {
    isConnected: false,
    connectionError: 'WebSocket已禁用',
    lastUpdate: 0,
    reconnectAttempts: 0,
    connect: () => Promise.resolve(),
    disconnect: () => {},
    sendMessage: () => console.log('🚫 WebSocket已禁用，无法发送消息'),
    updateSyncConfig: () => {},
    getConnectionStatus: () => ({ isConnected: false, reconnectAttempts: 0 })
  }

}

// 专门用于系统统计的实时数据Hook
export const useRealTimeStats = () => {
  const [stats, setStats] = useState({
    totalUsers: 1234,
    todayComments: 110,
    aiCalls: 2456,
    systemScore: 987
  })
  
  const [tokensData, setTokensData] = useState({
    hourly: [],
    daily: [],
    totalToday: 112960,
    totalWeek: 772260,
    costToday: 0.23,
    costWeek: 1.54
  })
  
  const [activities, setActivities] = useState([
    { user: '张老师', action: '生成了5条学生评语', time: '2分钟前', icon: '📝', color: '#51cf66' },
    { user: '李老师', action: '导入了新的学生数据', time: '15分钟前', icon: '📁', color: '#667eea' },
    { user: '王老师', action: '修改了AI配置参数', time: '1小时前', icon: '⚙️', color: '#ff6b6b' },
    { user: '赵老师', action: '导出了月度报告', time: '2小时前', icon: '📈', color: '#ffd43b' }
  ])
  
  // 处理用户活动更新
  const handleUserActivity = useCallback((data: any) => {
    setActivities(prev => [data, ...prev.slice(0, 9)]) // 保持最新10条记录
  }, [])
  
  // 处理tokens更新
  const handleTokensUpdate = useCallback((data: any) => {
    setTokensData(prev => ({
      ...prev,
      ...data
    }))
  }, [])
  
  // 处理评语生成事件
  const handleCommentGenerated = useCallback((data: any) => {
    setStats(prev => ({
      ...prev,
      todayComments: prev.todayComments + 1,
      aiCalls: prev.aiCalls + 1
    }))
  }, [])
  
  // 处理系统状态更新
  const handleSystemStatus = useCallback((data: any) => {
    setStats(prev => ({
      ...prev,
      ...data
    }))
  }, [])
  
  const { isConnected, connectionError, sendMessage } = useRealTimeData({
    onUserActivity: handleUserActivity,
    onTokensUpdate: handleTokensUpdate,
    onCommentGenerated: handleCommentGenerated,
    onSystemStatus: handleSystemStatus
  })
  
  return {
    stats,
    tokensData,
    activities,
    isConnected,
    connectionError,
    sendMessage
  }
}