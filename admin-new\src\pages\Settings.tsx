import React, { useState, useEffect } from 'react'
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  Tabs,
  Switch,
  InputNumber,
  Select,
  message,
  Divider,
  Avatar,
  Upload,
  Modal,
  notification
} from 'antd'
import { UserOutlined, UploadOutlined, SaveOutlined, SettingOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone, DownloadOutlined } from '@ant-design/icons'
import { useAuthStore } from '../stores/authStore'
import { useLocation, useNavigate } from 'react-router-dom'
import cloudFunctionService from '../services/cloudFunctionService'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

const Settings: React.FC = () => {
  const [form] = Form.useForm()
  const [profileForm] = Form.useForm()
  const [passwordForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('profile')
  const [passwordModalVisible, setPasswordModalVisible] = useState(false)
  const [avatarUrl, setAvatarUrl] = useState('')
  const { user } = useAuthStore()
  const location = useLocation()
  const navigate = useNavigate()

  // 根据URL参数设置活动标签页
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search)
    const tab = searchParams.get('tab')
    if (tab && ['profile', 'account', 'system'].includes(tab)) {
      setActiveTab(tab)
    }
  }, [location.search])

  // 更新URL参数
  const handleTabChange = (key: string) => {
    setActiveTab(key)
    const searchParams = new URLSearchParams(location.search)
    searchParams.set('tab', key)
    navigate(`${location.pathname}?${searchParams.toString()}`, { replace: true })
  }

  // 导出配置
  const handleExportConfig = () => {
    try {
      const config = {
        profile: profileForm.getFieldsValue(),
        system: form.getFieldsValue(),
        exportTime: new Date().toISOString(),
        version: '1.0.0'
      }

      const dataStr = JSON.stringify(config, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)

      const link = document.createElement('a')
      link.href = url
      link.download = `系统配置_${new Date().toISOString().slice(0, 10)}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      notification.success({
        message: '配置导出成功',
        description: '配置文件已下载到本地'
      })
    } catch (error) {
      notification.error({
        message: '配置导出失败',
        description: '请重试或联系管理员'
      })
    }
  }

  // 导入配置
  const handleImportConfig = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target?.result as string)

        if (config.profile) {
          profileForm.setFieldsValue(config.profile)
        }
        if (config.system) {
          form.setFieldsValue(config.system)
        }

        notification.success({
          message: '配置导入成功',
          description: '配置已恢复，请检查并保存'
        })
      } catch (error) {
        notification.error({
          message: '配置导入失败',
          description: '配置文件格式错误'
        })
      }
    }
    reader.readAsText(file)
    return false // 阻止默认上传行为
  }

  const onSystemSettingsFinish = async (values: any) => {
    setLoading(true)
    try {
      // 保存系统设置到本地存储
      localStorage.setItem('systemSettings', JSON.stringify(values))
      
      notification.success({
        message: '系统设置保存成功',
        description: '系统配置已成功保存！'
      })
    } catch (error) {
      notification.error({
        message: '系统设置保存失败',
        description: '请重试或联系管理员'
      })
    } finally {
      setLoading(false)
    }
  }

  const onProfileFinish = async (values: any) => {
    setLoading(true)
    try {
      // 保存个人信息到本地存储
      localStorage.setItem('profileSettings', JSON.stringify(values))
      
      notification.success({
        message: '个人信息更新成功',
        description: '个人信息已成功更新！'
      })
    } catch (error) {
      notification.error({
        message: '个人信息更新失败',
        description: '请重试或联系管理员'
      })
    } finally {
      setLoading(false)
    }
  }

  const onPasswordFinish = async (values: any) => {
    setLoading(true)
    try {
      // 模拟密码修改（实际应用中需要调用后端API验证旧密码）
      if (values.currentPassword === 'admin') {
        notification.success({
          message: '密码修改成功',
          description: '您的密码已成功修改，请妥善保管！'
        })
        setPasswordModalVisible(false)
        passwordForm.resetFields()
      } else {
        throw new Error('当前密码不正确')
      }
    } catch (error) {
      notification.error({
        message: '密码修改失败',
        description: error instanceof Error ? error.message : '请检查原密码是否正确'
      })
    } finally {
      setLoading(false)
    }
  }

  const uploadProps = {
    name: 'avatar',
    showUploadList: false,
    accept: 'image/*',
    beforeUpload: async (file: File) => {
      const isImage = file.type.startsWith('image/')
      if (!isImage) {
        notification.error({
          message: '文件格式错误',
          description: '只能上传图片文件！'
        })
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        notification.error({
          message: '文件过大',
          description: '图片大小不能超过2MB！'
        })
        return false
      }
      
      // 自定义上传逻辑
      try {
        setLoading(true)
        
        // 使用云函数上传
        const result = await cloudFunctionService.callFunction('uploadFile', {
          fileData: file,
          fileName: `avatar_${Date.now()}_${file.name}`,
          fileType: 'avatar'
        })
        
        if (result.code === 0 && result.data?.url) {
          setAvatarUrl(result.data.url)
          notification.success({
            message: '头像上传成功',
            description: '您的头像已成功更新！'
          })
        } else {
          throw new Error(result.message || '上传失败')
        }
      } catch (error) {
        console.error('头像上传失败:', error)
        notification.error({
          message: '头像上传失败',
          description: error instanceof Error ? error.message : '上传失败，请重试'
        })
      } finally {
        setLoading(false)
      }
      
      return false // 阻止默认上传行为
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6 transition-colors">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <Title level={1} className="!mb-2 theme-text-primary flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg">
                <SettingOutlined className="text-2xl text-white" />
              </div>
              系统设置
            </Title>
            <Text className="theme-text-secondary text-lg">
              管理系统配置和个人信息
            </Text>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold theme-text-primary mb-1">
              {new Date().toLocaleTimeString()}
            </div>
            <div className="theme-text-secondary">
              {new Date().toLocaleDateString('zh-CN', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                weekday: 'long' 
              })}
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-6">

        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-xl transition-colors">
          <Tabs
            activeKey={activeTab}
            onChange={handleTabChange}
            type="card"
            items={[
              {
                key: 'profile',
                label: '个人信息',
                children: (
            <Card title="个人资料" style={{ maxWidth: 800 }}>
              <div style={{ display: 'flex', alignItems: 'flex-start', gap: 24, marginBottom: 24 }}>
                <div style={{ textAlign: 'center' }}>
                  <Avatar size={80} icon={<UserOutlined />} />
                  <div style={{ marginTop: 12 }}>
                    <Upload {...uploadProps}>
                      <Button size="small" icon={<UploadOutlined />}>
                        更换头像
                      </Button>
                    </Upload>
                  </div>
                </div>
                <div style={{ flex: 1 }}>
                  <Form
                    form={profileForm}
                    layout="vertical"
                    onFinish={onProfileFinish}
                    initialValues={{
                      name: user?.name || '',
                      username: user?.username || '',
                      email: user?.email || '',
                      phone: '',
                      description: ''
                    }}
                  >
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 16 }}>
                      <Form.Item
                        label="姓名"
                        name="name"
                        rules={[{ required: true, message: '请输入姓名' }]}
                      >
                        <Input />
                      </Form.Item>

                      <Form.Item
                        label="用户名"
                        name="username"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input disabled />
                      </Form.Item>

                      <Form.Item
                        label="邮箱"
                        name="email"
                        rules={[
                          { required: true, message: '请输入邮箱' },
                          { type: 'email', message: '请输入有效的邮箱' }
                        ]}
                      >
                        <Input />
                      </Form.Item>

                      <Form.Item
                        label="手机号"
                        name="phone"
                      >
                        <Input />
                      </Form.Item>
                    </div>

                    <Form.Item
                      label="个人描述"
                      name="description"
                    >
                      <TextArea rows={3} placeholder="简单介绍一下自己..." />
                    </Form.Item>

                    <Form.Item>
                      <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                        保存个人信息
                      </Button>
                    </Form.Item>
                  </Form>
                </div>
              </div>
            </Card>
                )
              },
              {
                key: 'account',
                label: '账户设置',
                children: (
                  <>
            <Card title="密码管理" style={{ maxWidth: 800, marginBottom: 16 }}>
              <div className="mb-4">
                <Text type="secondary">
                  为了您的账户安全，建议定期更换密码。密码长度至少6位，建议包含字母、数字和特殊字符。
                </Text>
              </div>
              <Button
                type="primary"
                icon={<LockOutlined />}
                onClick={() => setPasswordModalVisible(true)}
              >
                修改密码
              </Button>
            </Card>

            <Card title="安全设置" style={{ maxWidth: 800, marginBottom: 16 }}>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">登录通知</div>
                    <div className="text-sm text-gray-500">账户登录时发送邮件通知</div>
                  </div>
                  <Switch defaultChecked />
                </div>
                <Divider />
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">双因素认证</div>
                    <div className="text-sm text-gray-500">使用手机验证码增强账户安全</div>
                  </div>
                  <Switch />
                </div>
                <Divider />
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">会话超时</div>
                    <div className="text-sm text-gray-500">设置自动登出时间</div>
                  </div>
                  <Select defaultValue="30" style={{ width: 120 }}>
                    <Option value="15">15分钟</Option>
                    <Option value="30">30分钟</Option>
                    <Option value="60">1小时</Option>
                    <Option value="120">2小时</Option>
                  </Select>
                </div>
              </div>
            </Card>

            <Card title="账户信息" style={{ maxWidth: 800 }}>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">用户ID:</span>
                  <span>{user?.id || 'admin_001'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">账户类型:</span>
                  <span>超级管理员</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">创建时间:</span>
                  <span>2024-01-01 10:00:00</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">最后登录:</span>
                  <span>{new Date().toLocaleString()}</span>
                </div>
              </div>
            </Card>
                  </>
                )
              },
              {
                key: 'system',
                label: '系统配置',
                children: (
            <Card title="基础设置" style={{ marginBottom: 16 }}>
              <Form
                form={form}
                layout="vertical"
                onFinish={onSystemSettingsFinish}
                initialValues={{
                  systemName: '评语灵感君管理后台',
                  version: 'v2.0.0',
                  maxUsers: 1000,
                  sessionTimeout: 30,
                  enableRegistration: false,
                  enableEmailNotify: true,
                  enableSMSNotify: false,
                  maintenanceMode: false,
                  logLevel: 'info',
                  backupFrequency: 'daily'
                }}
              >
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 16 }}>
                  <Form.Item
                    label="系统名称"
                    name="systemName"
                    rules={[{ required: true, message: '请输入系统名称' }]}
                  >
                    <Input />
                  </Form.Item>

                  <Form.Item
                    label="系统版本"
                    name="version"
                  >
                    <Input disabled />
                  </Form.Item>

                  <Form.Item
                    label="最大用户数"
                    name="maxUsers"
                    tooltip="系统支持的最大用户数量"
                  >
                    <InputNumber min={1} max={10000} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="会话超时(分钟)"
                    name="sessionTimeout"
                    tooltip="用户无操作后自动登出的时间"
                  >
                    <InputNumber min={5} max={120} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="日志级别"
                    name="logLevel"
                  >
                    <Select>
                      <Option value="debug">Debug</Option>
                      <Option value="info">Info</Option>
                      <Option value="warn">Warning</Option>
                      <Option value="error">Error</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    label="备份频率"
                    name="backupFrequency"
                  >
                    <Select>
                      <Option value="hourly">每小时</Option>
                      <Option value="daily">每天</Option>
                      <Option value="weekly">每周</Option>
                      <Option value="monthly">每月</Option>
                    </Select>
                  </Form.Item>
                </div>

                <Divider />

                <Title level={4}>功能开关</Title>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 16 }}>
                  <Form.Item
                    label="允许用户注册"
                    name="enableRegistration"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="邮件通知"
                    name="enableEmailNotify"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="短信通知"
                    name="enableSMSNotify"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="维护模式"
                    name="maintenanceMode"
                    valuePropName="checked"
                    tooltip="开启后只有管理员可以访问系统"
                  >
                    <Switch />
                  </Form.Item>
                </div>

                <Form.Item style={{ marginTop: 24 }}>
                  <Space>
                    <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                      保存配置
                    </Button>
                    <Button onClick={() => form.resetFields()}>
                      重置
                    </Button>
                    <Button icon={<DownloadOutlined />} onClick={handleExportConfig}>
                      导出配置
                    </Button>
                    <Upload
                      accept=".json"
                      showUploadList={false}
                      beforeUpload={handleImportConfig}
                    >
                      <Button icon={<UploadOutlined />}>
                        导入配置
                      </Button>
                    </Upload>
                  </Space>
                </Form.Item>
              </Form>
            </Card>
                )
              },
              {
                key: 'notifications',
                label: '通知设置',
                children: (
            <Card title="通知配置">
              <Form
                layout="vertical"
                initialValues={{
                  emailEnabled: true,
                  smsEnabled: false,
                  browserEnabled: true,
                  systemAlert: true,
                  userActivity: false,
                  dataBackup: true,
                  errorAlert: true
                }}
                onFinish={(values) => {
                  console.log('通知设置:', values)
                  message.success('通知设置保存成功！')
                }}
              >
                <Title level={4}>通知方式</Title>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16, marginBottom: 24 }}>
                  <Form.Item
                    label="邮件通知"
                    name="emailEnabled"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="短信通知"
                    name="smsEnabled"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="浏览器推送"
                    name="browserEnabled"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </div>

                <Divider />

                <Title level={4}>通知类型</Title>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16 }}>
                  <Form.Item
                    label="系统告警"
                    name="systemAlert"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="用户活动"
                    name="userActivity"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="数据备份"
                    name="dataBackup"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="错误报告"
                    name="errorAlert"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </div>

                <Form.Item style={{ marginTop: 24 }}>
                  <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                    保存通知设置
                  </Button>
                </Form.Item>
              </Form>
            </Card>
                )
              }
            ]}
          />
        </div>
      </div>

      {/* 密码修改模态框 */}
      <Modal
        title="修改密码"
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false)
          passwordForm.resetFields()
        }}
        footer={null}
        width={500}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={onPasswordFinish}
        >
          <Form.Item
            label="当前密码"
            name="currentPassword"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password
              placeholder="请输入当前密码"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item
            label="新密码"
            name="newPassword"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度至少6位' },
              {
                pattern: /^(?=.*[a-zA-Z])(?=.*\d)/,
                message: '密码必须包含字母和数字'
              }
            ]}
          >
            <Input.Password
              placeholder="请输入新密码（至少6位，包含字母和数字）"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item
            label="确认新密码"
            name="confirmPassword"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve()
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'))
                }
              })
            ]}
          >
            <Input.Password
              placeholder="请再次输入新密码"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item className="mb-0 mt-6">
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<LockOutlined />}
              >
                确认修改
              </Button>
              <Button
                onClick={() => {
                  setPasswordModalVisible(false)
                  passwordForm.resetFields()
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Settings