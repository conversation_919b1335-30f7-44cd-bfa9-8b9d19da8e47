/**
 * 环境配置工具
 * 提供类型安全的环境变量访问
 */

interface EnvConfig {
  // 应用配置
  APP_TITLE: string
  APP_VERSION: string
  APP_DESCRIPTION: string
  
  // API配置
  API_BASE_URL: string
  UPLOAD_BASE_URL: string
  
  // 微信云开发配置
  WECHAT_CLOUD_ENV_ID: string
  WECHAT_CLOUD_REGION: string
  
  // 功能开关
  ENABLE_MOCK: boolean
  ENABLE_DEVTOOLS: boolean
  ENABLE_PWA: boolean
  ENABLE_ERROR_REPORTING: boolean
  
  // 监控配置
  SENTRY_DSN?: string
  ANALYTICS_ID?: string
  
  // 第三方服务配置
  AI_PROVIDERS: string[]
  MAX_UPLOAD_SIZE: number
  SUPPORTED_FILE_TYPES: string[]
  
  // 缓存配置
  CACHE_TTL: number
  REQUEST_TIMEOUT: number
  MAX_RETRIES: number
  
  // 安全配置
  ENABLE_CSRF: boolean
  SESSION_TIMEOUT: number
  TOKEN_REFRESH_THRESHOLD: number
  
  // 性能配置
  ENABLE_COMPRESSION: boolean
  BUNDLE_ANALYZER: boolean
  SOURCE_MAP: boolean
  
  // 开发配置
  DEV_SERVER_PORT: number
  DEV_SERVER_HOST: string
  HMR_PORT: number
}

// 获取环境变量值，支持类型转换
const getEnvValue = <T>(key: string, defaultValue: T, transform?: (value: string) => T): T => {
  const value = import.meta.env[key]
  
  if (value === undefined || value === '') {
    return defaultValue
  }
  
  if (transform) {
    try {
      return transform(value)
    } catch (error) {
      console.warn(`Failed to transform env value for ${key}:`, error)
      return defaultValue
    }
  }
  
  return value as T
}

// 解析布尔值
const parseBoolean = (value: string): boolean => {
  return value.toLowerCase() === 'true'
}

// 解析数字
const parseNumber = (value: string): number => {
  const num = Number(value)
  if (isNaN(num)) {
    throw new Error(`Invalid number: ${value}`)
  }
  return num
}

// 解析数组
const parseArray = (value: string): string[] => {
  return value.split(',').map(item => item.trim()).filter(Boolean)
}

// 构建环境配置对象
export const env: EnvConfig = {
  // 应用配置
  APP_TITLE: getEnvValue('VITE_APP_TITLE', '评语灵感君管理后台'),
  APP_VERSION: getEnvValue('VITE_APP_VERSION', '2.0.0'),
  APP_DESCRIPTION: getEnvValue('VITE_APP_DESCRIPTION', '现代化AI评语生成系统管理平台'),
  
  // API配置
  API_BASE_URL: getEnvValue('VITE_API_BASE_URL', 'https://cloud1-4g85f8xlb8166ff1-1365982463.ap-shanghai.app.tcloudbase.com/adminAPI'),
  UPLOAD_BASE_URL: getEnvValue('VITE_UPLOAD_BASE_URL', 'https://cloud1-4g85f8xlb8166ff1-1365982463.ap-shanghai.app.tcloudbase.com/upload'),
  
  // 微信云开发配置
  WECHAT_CLOUD_ENV_ID: getEnvValue('VITE_WECHAT_CLOUD_ENV_ID', 'cloud1-4g85f8xlb8166ff1'),
  WECHAT_CLOUD_REGION: getEnvValue('VITE_WECHAT_CLOUD_REGION', 'ap-shanghai'),
  
  // 功能开关
  ENABLE_MOCK: getEnvValue('VITE_ENABLE_MOCK', false, parseBoolean), // 禁用模拟模式，使用真实数据
  ENABLE_DEVTOOLS: getEnvValue('VITE_ENABLE_DEVTOOLS', import.meta.env.DEV, parseBoolean),
  ENABLE_PWA: getEnvValue('VITE_ENABLE_PWA', false, parseBoolean),
  ENABLE_ERROR_REPORTING: getEnvValue('VITE_ENABLE_ERROR_REPORTING', false, parseBoolean),
  
  // 监控配置
  SENTRY_DSN: getEnvValue('VITE_SENTRY_DSN', undefined),
  ANALYTICS_ID: getEnvValue('VITE_ANALYTICS_ID', undefined),
  
  // 第三方服务配置
  AI_PROVIDERS: getEnvValue('VITE_AI_PROVIDERS', ['doubao'], parseArray),
  MAX_UPLOAD_SIZE: getEnvValue('VITE_MAX_UPLOAD_SIZE', 10485760, parseNumber), // 10MB
  SUPPORTED_FILE_TYPES: getEnvValue('VITE_SUPPORTED_FILE_TYPES', ['.jpg', '.png'], parseArray),
  
  // 缓存配置
  CACHE_TTL: getEnvValue('VITE_CACHE_TTL', 300000, parseNumber), // 5分钟
  REQUEST_TIMEOUT: getEnvValue('VITE_REQUEST_TIMEOUT', 30000, parseNumber), // 30秒
  MAX_RETRIES: getEnvValue('VITE_MAX_RETRIES', 3, parseNumber),
  
  // 安全配置
  ENABLE_CSRF: getEnvValue('VITE_ENABLE_CSRF', true, parseBoolean),
  SESSION_TIMEOUT: getEnvValue('VITE_SESSION_TIMEOUT', 3600000, parseNumber), // 1小时
  TOKEN_REFRESH_THRESHOLD: getEnvValue('VITE_TOKEN_REFRESH_THRESHOLD', 300000, parseNumber), // 5分钟
  
  // 性能配置
  ENABLE_COMPRESSION: getEnvValue('VITE_ENABLE_COMPRESSION', true, parseBoolean),
  BUNDLE_ANALYZER: getEnvValue('VITE_BUNDLE_ANALYZER', false, parseBoolean),
  SOURCE_MAP: getEnvValue('VITE_SOURCE_MAP', import.meta.env.DEV, parseBoolean),
  
  // 开发配置
  DEV_SERVER_PORT: getEnvValue('VITE_DEV_SERVER_PORT', 3000, parseNumber),
  DEV_SERVER_HOST: getEnvValue('VITE_DEV_SERVER_HOST', 'localhost'),
  HMR_PORT: getEnvValue('VITE_HMR_PORT', 3001, parseNumber)
}

// 环境检查函数
export const isDevelopment = (): boolean => import.meta.env.DEV
export const isProduction = (): boolean => import.meta.env.PROD
export const getMode = (): string => import.meta.env.MODE

// 调试信息输出（仅在开发环境）
if (isDevelopment()) {
  console.group('🔧 Environment Configuration')
  console.log('Mode:', getMode())
  console.log('API Base URL:', env.API_BASE_URL)
  console.log('Cloud Env ID:', env.WECHAT_CLOUD_ENV_ID)
  console.log('Enable Mock:', env.ENABLE_MOCK)
  console.log('Enable DevTools:', env.ENABLE_DEVTOOLS)
  console.groupEnd()
}

// 验证必需的环境变量
export const validateEnvironment = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = []
  
  // 检查必需的配置
  if (!env.WECHAT_CLOUD_ENV_ID || env.WECHAT_CLOUD_ENV_ID === 'your-env-id') {
    errors.push('VITE_WECHAT_CLOUD_ENV_ID is required')
  }
  
  if (!env.API_BASE_URL || env.API_BASE_URL.includes('your-')) {
    errors.push('VITE_API_BASE_URL is required')
  }
  
  // 检查端口冲突
  if (env.DEV_SERVER_PORT === env.HMR_PORT) {
    errors.push('DEV_SERVER_PORT and HMR_PORT cannot be the same')
  }
  
  // 检查上传大小限制
  if (env.MAX_UPLOAD_SIZE > 50 * 1024 * 1024) { // 50MB
    errors.push('MAX_UPLOAD_SIZE should not exceed 50MB')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

// 获取云函数URL构建器
export const buildCloudFunctionUrl = (functionName: string): string => {
  const baseUrl = env.API_BASE_URL.replace('/admin-api', '')
  return `${baseUrl}/${functionName}`
}

// 获取静态资源URL构建器
export const buildAssetUrl = (path: string): string => {
  if (path.startsWith('http')) {
    return path
  }
  return `${env.UPLOAD_BASE_URL}${path.startsWith('/') ? '' : '/'}${path}`
}

export default env