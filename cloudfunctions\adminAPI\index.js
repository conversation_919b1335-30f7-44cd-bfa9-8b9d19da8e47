/**
 * 智慧评语助手3.0 - 管理后台API云函数
 * 统一处理所有管理后台的API请求
 */

const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 获取数据库引用
const db = cloud.database()
const _ = db.command

// 导入各个模块的处理器
const authHandler = require('./handlers/authHandler')
const aiHandler = require('./handlers/aiHandler')
const dataHandler = require('./handlers/dataHandler')
const systemHandler = require('./handlers/systemHandler')
const realtimeHandler = require('./handlers/realtimeHandler')
const UserHandler = require('./handlers/userHandler')

// 通用响应格式
const createResponse = (code = 200, message = 'success', data = null) => {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

// 错误处理
const handleError = (error, action) => {
  console.error(`[AdminAPI] ${action} 执行失败:`, error)
  
  // 根据错误类型返回不同的错误码
  if (error.message.includes('权限')) {
    return createResponse(403, error.message)
  } else if (error.message.includes('参数')) {
    return createResponse(400, error.message)
  } else if (error.message.includes('不存在')) {
    return createResponse(404, error.message)
  } else {
    return createResponse(500, error.message || '服务器内部错误')
  }
}

// 权限验证中间件
const checkPermission = async (event, requiredPermission = null) => {
  const { OPENID } = cloud.getWXContext()
  
  // 如果需要特定权限，检查管理员权限
  if (requiredPermission) {
    const adminCollection = db.collection('admins')
    const adminResult = await adminCollection.where({
      openid: OPENID,
      status: 'active'
    }).get()
    
    if (adminResult.data.length === 0) {
      throw new Error('您不是管理员，无权访问此功能')
    }
    
    const admin = adminResult.data[0]
    
    // 检查具体权限
    if (admin.role !== 'super_admin' && !admin.permissions.includes(requiredPermission)) {
      throw new Error(`您没有执行 ${requiredPermission} 操作的权限`)
    }
    
    return admin
  }
  
  return { openid: OPENID }
}

// 记录操作日志
const logOperation = async (action, admin, data = {}, result = 'success') => {
  try {
    await db.collection('logs').add({
      data: {
        action,
        adminId: admin.id || admin.openid,
        adminName: admin.profile?.name || admin.username || '未知管理员',
        requestData: data,
        result,
        ip: '',  // 云函数中难以获取真实IP
        userAgent: '',
        createTime: db.serverDate(),
        timestamp: Date.now()
      }
    })
  } catch (error) {
    console.error('[AdminAPI] 记录操作日志失败:', error)
  }
}

// 主函数 - 支持HTTP触发器和普通调用
exports.main = async (event, context) => {
  console.log('[AdminAPI] 收到请求:', event)
  
  // 处理HTTP触发器请求
  if (event.httpMethod) {
    try {
      let requestData
      
      // 解析HTTP请求数据
      if (event.httpMethod === 'POST') {
        requestData = typeof event.body === 'string' ? JSON.parse(event.body) : event.body
      } else if (event.httpMethod === 'GET') {
        requestData = event.queryStringParameters || {}
      } else if (event.httpMethod === 'OPTIONS') {
        // 预检请求直接返回，不需要特殊处理
        return {
          statusCode: 200,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-WX-SOURCE, X-WX-OPENID, X-Requested-With',
            'Access-Control-Max-Age': '86400'
          },
          body: ''
        }
      } else {
        return {
          statusCode: 405,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          },
          body: JSON.stringify(createResponse(405, 'Method Not Allowed'))
        }
      }
      
      // 执行业务逻辑
      const result = await processRequest(requestData, context)
      
      // 返回HTTP响应
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-WX-SOURCE, X-WX-OPENID, X-Requested-With',
          'Access-Control-Max-Age': '86400',
          'Access-Control-Allow-Credentials': 'false'
        },
        body: JSON.stringify(result)
      }
      
    } catch (error) {
      console.error('HTTP请求处理失败:', error)
      
      return {
        statusCode: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-WX-SOURCE, X-WX-OPENID, X-Requested-With',
          'Access-Control-Allow-Credentials': 'false'
        },
        body: JSON.stringify(createResponse(500, '服务器内部错误', error.message))
      }
    }
  }
  
  // 处理普通云函数调用
  return await processRequest(event, context)
}

// 处理请求的核心逻辑
async function processRequest(event, context) {
  const { action, requestId, timestamp, ...requestData } = event
  
  if (!action) {
    return createResponse(400, '缺少action参数')
  }
  
  try {
    // 解析action，格式为 module.method
    const [module, method] = action.split('.')
    
    if (!module || !method) {
      return createResponse(400, '无效的action格式，应为 module.method')
    }
    
    let result
    let admin = null
    
    // 根据模块分发请求
    switch (module) {
      case 'auth':
        // 认证模块，某些操作不需要权限验证
        if (['login', 'checkAdminExists', 'initAdmin'].includes(method)) {
          result = await authHandler[method](requestData, db, cloud)
        } else {
          admin = await checkPermission({ action, requestId, timestamp, ...requestData })
          result = await authHandler[method](requestData, db, cloud, admin)
        }
        break
        
      case 'ai':
        admin = await checkPermission({ action, requestId, timestamp, ...requestData }, 'ai_manage')
        result = await aiHandler[method](requestData, db, cloud, admin)
        break
        
      case 'data':
        admin = await checkPermission({ action, requestId, timestamp, ...requestData }, 'data_manage')
        result = await dataHandler[method](requestData, db, cloud, admin)
        break
        
      case 'system':
        admin = await checkPermission({ action, requestId, timestamp, ...requestData }, 'system_manage')
        result = await systemHandler[method](requestData, db, cloud, admin)
        break
        
      case 'realtime':
        admin = await checkPermission({ action, requestId, timestamp, ...requestData })
        result = await realtimeHandler[method](requestData, db, cloud, admin)
        break

      case 'user':
        const UserHandler = require('./handlers/userHandler');
        const userHandler = new UserHandler(db);
        admin = await checkPermission({ action, requestId, timestamp, ...requestData }, 'user_manage');
        result = await userHandler[method](requestData);
        break
        
      default:
        // 处理单独的action（如healthCheck）
        if (action === 'healthCheck') {
          return createResponse(200, '服务正常', { 
            timestamp: Date.now(),
            version: '3.0.0',
            env: cloud.DYNAMIC_CURRENT_ENV,
            source: requestData.source || 'unknown'
          })
        }
        
        // 处理代理dataQuery的请求（避免CORS问题）
        if (action === 'proxy_dataQuery') {
          console.log('[AdminAPI] 代理调用dataQuery:', requestData.data)
          try {
            const proxyResult = await cloud.callFunction({
              name: 'dataQuery',
              data: requestData.data
            })
            console.log('[AdminAPI] dataQuery代理调用成功:', proxyResult.result)
            return proxyResult.result
          } catch (proxyError) {
            console.error('[AdminAPI] dataQuery代理调用失败:', proxyError)
            throw new Error(`dataQuery调用失败: ${proxyError.message}`)
          }
        }

        // 处理通用云函数代理请求（用于管理后台直接调用）
        if (action === 'proxy.cloudFunction') {
          const { functionName, functionData = {} } = requestData
          console.log(`[AdminAPI] 代理调用云函数: ${functionName}`, functionData)
          
          try {
            const proxyResult = await cloud.callFunction({
              name: functionName,
              data: functionData
            })
            console.log(`[AdminAPI] 云函数代理调用成功: ${functionName}`, proxyResult.result)
            return proxyResult.result
          } catch (proxyError) {
            console.error(`[AdminAPI] 云函数代理调用失败: ${functionName}`, proxyError)
            throw new Error(`${functionName}调用失败: ${proxyError.message}`)
          }
        }
        
        return createResponse(400, `未知的模块: ${module}`)
    }
    
    // 记录操作日志（排除敏感操作）
    if (admin && !['login', 'getUserInfo', 'healthCheck'].includes(method)) {
      await logOperation(action, admin, requestData, 'success')
    }
    
    return createResponse(200, '操作成功', result)
    
  } catch (error) {
    // 记录失败日志（安全检查admin变量）
    try {
      if (typeof admin !== 'undefined' && admin) {
        await logOperation(action, admin, requestData, 'failed')
      }
    } catch (logError) {
      console.error('记录失败日志时出错:', logError)
    }

    return handleError(error, action)
  }
}
