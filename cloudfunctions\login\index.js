/**
 * 用户登录云函数
 * 处理微信小程序用户登录和身份验证
 */

const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  console.log('用户登录请求:', event)
  
  try {
    // 获取微信用户信息
    const { OPENID } = cloud.getWXContext()
    const { userInfo, role = 'teacher' } = event
    
    if (!OPENID) {
      throw new Error('无法获取用户OpenID')
    }
    
    // 检查用户是否已存在
    const userResult = await db.collection('users').where({
      openid: OPENID
    }).get()
    
    let user
    const currentTime = new Date()
    
    if (userResult.data.length === 0) {
      // 新用户，创建用户记录
      const userData = {
        openid: OPENID,
        role: role,
        profile: userInfo || {},
        createTime: currentTime,
        lastLoginTime: currentTime,
        status: 'active',
        loginCount: 1
      }
      
      const createResult = await db.collection('users').add({
        data: userData
      })
      
      user = {
        _id: createResult._id,
        ...userData
      }
      
      console.log('新用户注册成功:', user)
    } else {
      // 老用户，更新登录信息
      user = userResult.data[0]
      
      await db.collection('users').doc(user._id).update({
        data: {
          lastLoginTime: currentTime,
          loginCount: db.command.inc(1),
          profile: userInfo ? db.command.set(userInfo) : user.profile
        }
      })
      
      user.lastLoginTime = currentTime
      user.loginCount = (user.loginCount || 0) + 1
      if (userInfo) user.profile = userInfo
      
      console.log('用户登录成功:', user.openid)
    }
    
    // 返回用户信息
    return {
      code: 200,
      message: '登录成功',
      data: {
        userId: user._id,
        openid: user.openid,
        role: user.role,
        profile: user.profile,
        isNewUser: userResult.data.length === 0,
        lastLoginTime: user.lastLoginTime,
        loginCount: user.loginCount
      }
    }
    
  } catch (error) {
    console.error('用户登录失败:', error)
    
    return {
      code: 500,
      message: error.message || '登录失败',
      data: null
    }
  }
}