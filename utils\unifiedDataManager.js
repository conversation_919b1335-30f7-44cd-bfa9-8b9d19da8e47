/**
 * 统一数据管理器
 * 解决数据实时连通性问题的核心组件
 */

const { dataEventBus } = require('./dataEventBus');

class UnifiedDataManager {
  constructor() {
    this.isInitialized = false;
    this.cloudService = null;
    this.dataCache = new Map();
    this.loadingStates = new Map();
    
    console.log('🎯 统一数据管理器初始化');
  }

  /**
   * 初始化数据管理器
   */
  async init() {
    if (this.isInitialized) return;
    
    try {
      const app = getApp();
      this.cloudService = app.globalData.cloudService;
      
      if (!this.cloudService) {
        throw new Error('云服务未初始化');
      }
      
      this.isInitialized = true;
      console.log('✅ 统一数据管理器初始化成功');
      
    } catch (error) {
      console.error('❌ 统一数据管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取学生数据（统一入口）
   * @param {Object} options - 选项
   * @param {string} options.pageId - 页面标识
   * @param {boolean} options.forceRefresh - 是否强制刷新
   * @param {string} options.classId - 班级ID筛选
   */
  async getStudents(options = {}) {
    const { pageId = 'unknown', forceRefresh = false, classId = null } = options;
    
    await this.init();
    
    const cacheKey = `students_${classId || 'all'}`;
    
    // 检查是否正在加载
    if (this.loadingStates.get(cacheKey)) {
      console.log(`⏳ 学生数据正在加载中，等待结果... (${pageId})`);
      return this.waitForLoading(cacheKey);
    }
    
    // 检查缓存（如果不强制刷新）
    if (!forceRefresh && this.dataCache.has(cacheKey)) {
      const cachedData = this.dataCache.get(cacheKey);
      console.log(`📦 返回缓存的学生数据 (${pageId}):`, cachedData.length);
      return { success: true, data: cachedData, source: 'cache' };
    }
    
    // 检查清空标记（智能过期机制）
    const studentsJustCleared = wx.getStorageSync('studentsJustCleared');
    const clearTimestamp = wx.getStorageSync('clearTimestamp') || 0;
    const now = Date.now();
    
    // 如果清空标记超过5分钟，自动清除
    if (studentsJustCleared && (now - clearTimestamp) > 5 * 60 * 1000) {
      wx.removeStorageSync('studentsJustCleared');
      wx.removeStorageSync('clearTimestamp');
      console.log('🕐 数据清空标记已过期，自动清除');
    } else if (studentsJustCleared && !forceRefresh) {
      console.log(`🚫 检测到数据已清空，返回空数据 (${pageId})`);
      const emptyData = [];
      this.dataCache.set(cacheKey, emptyData);
      
      // 通知所有订阅者
      dataEventBus.publish('students', emptyData, 'clear', pageId);
      
      return { success: true, data: emptyData, source: 'cleared' };
    }
    
    // 从云端加载数据
    return this.loadStudentsFromCloud(cacheKey, pageId, classId);
  }

  /**
   * 从云端加载学生数据
   */
  async loadStudentsFromCloud(cacheKey, pageId, classId) {
    this.loadingStates.set(cacheKey, true);
    
    try {
      console.log(`☁️ 从云端加载学生数据 (${pageId})...`);
      
      const result = await this.cloudService.getStudentList(classId);
      
      if (result.success) {
        const students = result.data || [];
        
        // 更新缓存
        this.dataCache.set(cacheKey, students);
        
        // 通知所有订阅者（除了当前页面）
        dataEventBus.publish('students', students, 'refresh', pageId);
        
        console.log(`✅ 学生数据加载成功 (${pageId}):`, students.length);
        
        return { success: true, data: students, source: 'cloud' };
      } else {
        throw new Error(result.error || '获取学生数据失败');
      }
      
    } catch (error) {
      console.error(`❌ 学生数据加载失败 (${pageId}):`, error);
      return { success: false, error: error.message, source: 'error' };
      
    } finally {
      this.loadingStates.delete(cacheKey);
    }
  }

  /**
   * 等待加载完成
   */
  async waitForLoading(cacheKey, maxWait = 10000) {
    const startTime = Date.now();
    
    while (this.loadingStates.get(cacheKey) && (Date.now() - startTime) < maxWait) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    if (this.dataCache.has(cacheKey)) {
      return { success: true, data: this.dataCache.get(cacheKey), source: 'waited' };
    } else {
      return { success: false, error: '等待加载超时', source: 'timeout' };
    }
  }

  /**
   * 清空学生数据（统一入口）
   */
  async clearStudents(pageId = 'unknown') {
    await this.init();
    
    console.log(`🧹 开始清空学生数据 (${pageId})...`);
    
    try {
      // 1. 清空云端数据
      let cloudResult = { success: true, message: '跳过云端清空' };
      if (this.cloudService.clearAllStudents) {
        cloudResult = await this.cloudService.clearAllStudents();
      }
      
      // 2. 清空本地缓存
      this.dataCache.clear();
      
      // 3. 设置清空标记
      wx.setStorageSync('studentsJustCleared', true);
      wx.setStorageSync('clearTimestamp', Date.now());
      
      // 4. 通知所有订阅者数据已清空
      dataEventBus.publish('students', [], 'clear', pageId);
      dataEventBus.clearCache('students');
      
      console.log(`✅ 学生数据清空完成 (${pageId})`);
      
      return {
        success: cloudResult.success,
        message: cloudResult.message,
        source: 'unified'
      };
      
    } catch (error) {
      console.error(`❌ 学生数据清空失败 (${pageId}):`, error);
      return {
        success: false,
        error: error.message,
        source: 'error'
      };
    }
  }

  /**
   * 添加学生（统一入口）
   */
  async addStudent(studentData, pageId = 'unknown') {
    await this.init();

    try {
      console.log(`➕ 添加学生 (${pageId}):`, studentData.name);

      const result = await this.cloudService.addStudent(studentData);

      if (result.success) {
        // 如果学生有班级信息，同步更新classes集合
        if (studentData.className && studentData.className.trim()) {
          await this.syncClassInfo(studentData.className, studentData);
        }

        // 清除缓存，强制下次重新加载
        this.dataCache.clear();

        // 清除"数据已清空"标记，允许重新加载数据
        wx.removeStorageSync('studentsJustCleared');
        wx.removeStorageSync('clearTimestamp');
        console.log('🎯 清除数据清空标记，允许重新加载学生数据');

        // 立即重新加载数据
        const freshData = await this.getStudents({ pageId, forceRefresh: true });

        // 主动通知所有页面学生数据已更新
        if (freshData.success) {
          dataEventBus.publish('students', freshData.data, 'add', pageId);
        }

        console.log(`✅ 学生添加成功，已通知所有页面 (${pageId})`);
      }

      return result;
      
    } catch (error) {
      console.error(`❌ 学生添加失败 (${pageId}):`, error);
      return { success: false, error: error.message };
    }
  },

  /**
   * 同步班级信息到classes集合
   */
  async syncClassInfo(className, studentData) {
    try {
      console.log(`🏫 同步班级信息: ${className}`);

      // 调用云函数同步班级信息
      const result = await wx.cloud.callFunction({
        name: 'addStudent', // 复用addStudent云函数，添加班级同步逻辑
        data: {
          action: 'syncClass',
          className: className,
          studentInfo: {
            name: studentData.name,
            studentId: studentData.studentId || studentData.studentNumber,
            gender: studentData.gender
          }
        }
      });

      if (result.result && result.result.success) {
        console.log(`✅ 班级信息同步成功: ${className}`);
      } else {
        console.warn(`⚠️ 班级信息同步失败: ${result.result?.error || '未知错误'}`);
      }
    } catch (error) {
      console.error(`❌ 班级信息同步异常:`, error);
    }
  }

  /**
   * 删除学生（统一入口）
   */
  async deleteStudent(studentId, pageId = 'unknown') {
    await this.init();
    
    try {
      console.log(`🗑️ 删除学生 (${pageId}):`, studentId);
      
      const result = await this.cloudService.deleteStudent(studentId);
      
      if (result.success) {
        // 清除缓存，强制下次重新加载
        this.dataCache.clear();
        
        // 立即重新加载数据
        const freshData = await this.getStudents({ pageId, forceRefresh: true });
        
        // 主动通知所有页面学生数据已更新
        if (freshData.success) {
          dataEventBus.publish('students', freshData.data, 'delete', pageId);
        }
        
        console.log(`✅ 学生删除成功，已通知所有页面 (${pageId})`);
      }
      
      return result;
      
    } catch (error) {
      console.error(`❌ 学生删除失败 (${pageId}):`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取班级数据（统一入口）
   */
  async getClasses(options = {}) {
    const { pageId = 'unknown', forceRefresh = false } = options;
    
    await this.init();
    
    const cacheKey = 'classes';
    
    // 检查缓存
    if (!forceRefresh && this.dataCache.has(cacheKey)) {
      const cachedData = this.dataCache.get(cacheKey);
      console.log(`📦 返回缓存的班级数据 (${pageId}):`, cachedData.length);
      return { success: true, data: cachedData, source: 'cache' };
    }
    
    try {
      console.log(`☁️ 从云端加载班级数据 (${pageId})...`);
      
      const result = await this.cloudService.getClassList();
      
      if (result.success) {
        const classes = result.data || [];
        
        // 更新缓存
        this.dataCache.set(cacheKey, classes);
        
        // 通知所有订阅者
        dataEventBus.publish('classes', classes, 'refresh', pageId);
        
        console.log(`✅ 班级数据加载成功 (${pageId}):`, classes.length);
        
        return { success: true, data: classes, source: 'cloud' };
      } else {
        throw new Error(result.error || '获取班级数据失败');
      }
      
    } catch (error) {
      console.error(`❌ 班级数据加载失败 (${pageId}):`, error);
      return { success: false, error: error.message, source: 'error' };
    }
  }



  /**
   * 获取数据状态
   */
  getDataStatus() {
    return {
      isInitialized: this.isInitialized,
      cacheKeys: Array.from(this.dataCache.keys()),
      loadingStates: Array.from(this.loadingStates.keys()),
      eventBusStatus: dataEventBus.getSubscriptionStatus(),
      clearStatus: {
        studentsJustCleared: wx.getStorageSync('studentsJustCleared'),
        clearTimestamp: wx.getStorageSync('clearTimestamp')
      }
    };
  }
}

// 创建全局单例
const unifiedDataManager = new UnifiedDataManager();

module.exports = {
  unifiedDataManager,

  // 便捷方法
  getStudents: (options) => unifiedDataManager.getStudents(options),
  clearStudents: (pageId) => unifiedDataManager.clearStudents(pageId),
  addStudent: (data, pageId) => unifiedDataManager.addStudent(data, pageId),
  deleteStudent: (id, pageId) => unifiedDataManager.deleteStudent(id, pageId),
  getClasses: (options) => unifiedDataManager.getClasses(options),
  getDataStatus: () => unifiedDataManager.getDataStatus()
};
